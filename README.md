# jarinker

A JAR shrinker that analyzes dependencies and removes unused classes to reduce JAR file sizes.

## 🚀 Quick Start

### One-Line Execution (Recommended)

No installation required! Use jarinker directly with a single command:

**Linux/macOS:**
```bash
# Analyze dependencies
curl -sSL https://raw.githubusercontent.com/DanielLiu1123/jarinker/main/scripts/jarinker | bash -s -- analyze -cp libs/ build/classes/java/main

# Shrink JARs
curl -sSL https://raw.githubusercontent.com/DanielLiu1123/jarinker/main/scripts/jarinker | bash -s -- shrink -cp libs/ -o shrunk-libs/ build/classes/java/main
```

**Windows (PowerShell):**
```powershell
# Download and run
Invoke-WebRequest -Uri "https://raw.githubusercontent.com/DanielLiu1123/jarinker/main/scripts/jarinker.ps1" -OutFile "jarinker.ps1"
.\jarinker.ps1 analyze -cp libs/ build/classes/java/main
```

### Install Once, Use Everywhere

```bash
# Install jarinker
curl -sSL https://raw.githubusercontent.com/DanielLiu1123/jarinker/main/scripts/install.sh | bash

# Use anywhere
jarinker analyze -cp libs/ build/classes/java/main
jarinker shrink -cp libs/ -o shrunk-libs/ build/classes/java/main
```

### Traditional Installation

If you prefer to build from source:

```bash
# Clone the repository
git clone https://github.com/DanielLiu1123/jarinker.git
cd jarinker

# Build the project
./gradlew build

# Install distribution
./gradlew :jarinker-cli:installDist

# Use jarinker
./jarinker-cli/build/install/jarinker/bin/jarinker --help
```

## 📋 Requirements

- **Java 17+**: Required to run jarinker
- **Network**: Required for first-time download (cached afterwards)

## 💡 Features

- **Dependency Analysis**: Analyze JAR dependencies at class, package, or module level
- **JAR Shrinking**: Remove unused classes to reduce JAR file sizes
- **Zero Installation**: Use directly without downloading or installing
- **Smart Caching**: Downloaded versions are cached for offline use
- **Cross-Platform**: Works on Linux, macOS, and Windows
- **Version Management**: Use specific versions or always get the latest

## 🔧 Usage Examples

### Analyze Dependencies

```bash
# Basic analysis
jarinker analyze -cp "libs/*" build/classes/java/main

# Package-level analysis with verbose output
jarinker analyze --type package -cp "libs/*" build/classes/java/main

# Show JDK dependencies
jarinker analyze --show-jdk-deps -cp "libs/*" build/classes/java/main
```

### Shrink JARs

```bash
# Shrink to output directory
jarinker shrink -cp "libs/*" -o shrunk-libs/ build/classes/java/main

# In-place shrinking (modifies original JARs)
jarinker shrink -cp "libs/*" build/classes/java/main

# Shrink specific JAR patterns
jarinker shrink --jar-pattern "guava-*.jar" -cp "libs/*" -o shrunk-libs/ build/classes/java/main
```

## ⚙️ Configuration

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `JARINKER_VERSION` | `latest` | Specify jarinker version |
| `JARINKER_CACHE_DIR` | `~/.jarinker` | Cache directory for downloads |
| `JARINKER_OFFLINE` | `false` | Use offline mode (cache only) |
| `JARINKER_VERBOSE` | `false` | Enable verbose output |

### Examples

```bash
# Use specific version
JARINKER_VERSION=0.1.0-RC1 jarinker analyze -cp libs/ build/

# Verbose mode
JARINKER_VERBOSE=true jarinker analyze -cp libs/ build/

# Offline mode (use cached version only)
JARINKER_OFFLINE=true jarinker analyze -cp libs/ build/
```

## 📊 Example Results

### Before Shrinking
```
Original JARs:
  guava-33.4.8-jre.jar: 2.7 MB
  Total size: 2.7 MB
```

### After Shrinking
```
Shrunk JARs:
  guava-33.4.8-jre.jar: 847 KB (68% reduction)
  Total size: 847 KB
  Space saved: 1.9 MB
```

## 🛠️ Development

See [SPEC.md](SPEC.md) for detailed specifications and [scripts/README.md](scripts/README.md) for remote execution script documentation.

### Building

```bash
./gradlew build
```

### Running Tests

```bash
./gradlew test
```

### Creating Native Image

```bash
./gradlew :jarinker-cli:nativeCompile
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the [MIT License](LICENSE).
