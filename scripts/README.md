# Jarinker Remote Execution Scripts

这些脚本允许用户无需手动下载和安装就能直接使用jarinker，实现真正的"一行命令"体验。

## 🚀 快速开始

### Linux/macOS (推荐)

**方式1：直接执行（推荐）**
```bash
# 分析依赖
curl -sSL https://raw.githubusercontent.com/DanielLiu1123/jarinker/main/scripts/jarinker | bash -s -- analyze -cp libs/ build/classes/java/main

# 缩减JAR
curl -sSL https://raw.githubusercontent.com/DanielLiu1123/jarinker/main/scripts/jarinker | bash -s -- shrink -cp libs/ -o shrunk-libs/ build/classes/java/main
```

**方式2：一键安装**
```bash
# 安装到 ~/.local/bin
curl -sSL https://raw.githubusercontent.com/DanielLiu1123/jarinker/main/scripts/install.sh | bash

# 使用
jarinker analyze -cp libs/ build/classes/java/main
jarinker shrink -cp libs/ -o shrunk-libs/ build/classes/java/main
```

**方式3：下载脚本使用**
```bash
# 下载脚本
curl -sSL https://raw.githubusercontent.com/DanielLiu1123/jarinker/main/scripts/jarinker -o jarinker && chmod +x jarinker

# 使用
./jarinker analyze -cp libs/ build/classes/java/main
./jarinker shrink -cp libs/ -o shrunk-libs/ build/classes/java/main
```

### Windows (PowerShell)

**直接执行**
```powershell
# 分析依赖
Invoke-WebRequest -Uri "https://raw.githubusercontent.com/DanielLiu1123/jarinker/main/scripts/jarinker.ps1" -UseBasicParsing | Invoke-Expression; .\jarinker.ps1 analyze -cp libs/ build/classes/java/main

# 下载后使用
Invoke-WebRequest -Uri "https://raw.githubusercontent.com/DanielLiu1123/jarinker/main/scripts/jarinker.ps1" -OutFile "jarinker.ps1"
.\jarinker.ps1 analyze -cp libs/ build/classes/java/main
```

## 📋 系统要求

- **Java**: 17 或更高版本
- **网络**: 首次使用需要网络连接下载jarinker
- **工具**: curl, unzip (Linux/macOS), PowerShell 3.0+ (Windows)

## ⚙️ 配置选项

### 环境变量

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `JARINKER_VERSION` | `latest` | 指定jarinker版本 |
| `JARINKER_CACHE_DIR` | `~/.jarinker` | 缓存目录 |
| `JARINKER_OFFLINE` | `false` | 离线模式，仅使用缓存 |
| `JARINKER_VERBOSE` | `false` | 详细输出模式 |


### 脚本选项

**Linux/macOS脚本选项：**
- `--script-help`: 显示脚本帮助
- `--script-version`: 显示脚本版本
- `--script-clean`: 清理缓存目录
- `--script-update`: 强制更新到最新版本

**Windows PowerShell选项：**
- `-ScriptHelp`: 显示脚本帮助
- `-ScriptVersion`: 显示脚本版本
- `-ScriptClean`: 清理缓存目录
- `-ScriptUpdate`: 强制更新到最新版本
- `-Verbose`: 启用详细输出

## 💡 使用示例

### 基本使用

```bash
# 分析项目依赖
./jarinker analyze -cp "libs/*" build/classes/java/main

# 缩减JAR文件
./jarinker shrink -cp "libs/*" -o shrunk-libs build/classes/java/main

# 显示帮助
./jarinker --help
```

### 高级使用

```bash
# 使用特定版本
JARINKER_VERSION=0.1.0-RC1 ./jarinker analyze -cp libs/ build/

# 详细输出模式
JARINKER_VERBOSE=true ./jarinker analyze -cp libs/ build/

# 离线模式（仅使用缓存）
JARINKER_OFFLINE=true ./jarinker analyze -cp libs/ build/

# 自定义缓存目录
JARINKER_CACHE_DIR=/tmp/jarinker-cache ./jarinker analyze -cp libs/ build/
```

### 脚本管理

```bash
# 查看脚本帮助
./jarinker --script-help

# 清理缓存
./jarinker --script-clean

# 强制更新到最新版本
./jarinker --script-update

# 查看脚本版本
./jarinker --script-version
```

## 🗂️ 缓存机制

脚本会自动缓存下载的jarinker版本，避免重复下载：

```
~/.jarinker/
├── versions/
│   ├── 0.1.0-RC1/
│   │   ├── jarinker-cli-0.1.0-RC1.zip
│   │   └── jarinker-cli-0.1.0-RC1/
│   │       └── bin/jarinker
│   └── 0.2.0/
│       ├── jarinker-cli-0.2.0.zip
│       └── jarinker-cli-0.2.0/
│           └── bin/jarinker
```

## 🔧 故障排除

### 常见问题

**1. Java版本不兼容**
```
Error: Java 17 or later is required. Found Java 8.
```
解决：升级Java到17或更高版本

**2. 网络连接问题**
```
Error: Failed to fetch latest version from GitHub API
```
解决：检查网络连接，或使用离线模式

**3. 权限问题**
```
Error: Failed to create installation directory
```
解决：使用sudo或选择有写权限的目录

**4. 缓存损坏**
```
Error: jarinker executable not found in extracted files
```
解决：清理缓存后重试
```bash
./jarinker --script-clean
```

### 调试模式

启用详细输出查看详细信息：
```bash
JARINKER_VERBOSE=true ./jarinker analyze -cp libs/ build/
```

## 🔒 安全考虑

1. **HTTPS传输**: 所有下载都使用HTTPS
2. **文件完整性**: 脚本会验证下载文件的存在性
3. **权限控制**: 脚本只在用户目录下操作
4. **代码审查**: 建议在使用前审查脚本内容

## 🤝 贡献

如果你发现问题或有改进建议，请：

1. 提交Issue描述问题
2. 提交Pull Request修复问题
3. 在讨论区分享使用经验

## 📄 许可证

这些脚本遵循与jarinker项目相同的许可证。

---

**享受一行命令的便利！** 🎉
