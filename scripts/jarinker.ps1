# Jarinker Remote Execution Script for Windows PowerShell
# This script automatically downloads and executes jarinker without requiring manual installation
# Usage: Invoke-WebRequest -Uri "https://raw.githubusercontent.com/DanielLiu1123/jarinker/main/scripts/jarinker.ps1" -UseBasicParsing | Invoke-Expression

param(
    [Parameter(ValueFromRemainingArguments = $true)]
    [string[]]$Arguments,
    
    [switch]$ScriptHelp,
    [switch]$ScriptVersion,
    [switch]$ScriptClean,
    [switch]$ScriptUpdate,
    [switch]$Verbose
)

# Global configuration
$Script:GITHUB_REPO = "DanielLiu1123/jarinker"
$Script:REQUIRED_JAVA_VERSION = 17
$Script:DEFAULT_CACHE_DIR = Join-Path $env:USERPROFILE ".jarinker"

# User configurable variables
$Script:JARINKER_CACHE_DIR = if ($env:JARINKER_CACHE_DIR) { $env:JARINKER_CACHE_DIR } else { $Script:DEFAULT_CACHE_DIR }
$Script:JARINKER_VERSION = if ($env:JARINKER_VERSION) { $env:JARINKER_VERSION } else { "latest" }
$Script:JARINKER_OFFLINE = if ($env:JARINKER_OFFLINE -eq "true") { $true } else { $false }
$Script:JARINKER_VERBOSE = if ($env:JARINKER_VERBOSE -eq "true" -or $Verbose) { $true } else { $false }

# Logging functions
function Write-Info {
    param([string]$Message)
    if ($Script:JARINKER_VERBOSE) {
        Write-Host "[INFO] $Message" -ForegroundColor Blue
    }
}

function Write-Warn {
    param([string]$Message)
    Write-Host "[WARN] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

function Write-Success {
    param([string]$Message)
    if ($Script:JARINKER_VERBOSE) {
        Write-Host "[SUCCESS] $Message" -ForegroundColor Green
    }
}

# Show help information
function Show-Help {
    @"
Jarinker Remote Execution Script for Windows

USAGE:
    # Direct execution (PowerShell 3.0+)
    Invoke-WebRequest -Uri "https://raw.githubusercontent.com/DanielLiu1123/jarinker/main/scripts/jarinker.ps1" -UseBasicParsing | Invoke-Expression
    
    # Download and use
    Invoke-WebRequest -Uri "https://raw.githubusercontent.com/DanielLiu1123/jarinker/main/scripts/jarinker.ps1" -OutFile "jarinker.ps1"
    .\jarinker.ps1 [JARINKER_ARGS]

ENVIRONMENT VARIABLES:
    JARINKER_VERSION     Specify jarinker version (default: latest)
    JARINKER_CACHE_DIR   Cache directory (default: ~/.jarinker)
    JARINKER_OFFLINE     Use offline mode, only use cached versions (default: false)
    JARINKER_VERBOSE     Enable verbose output (default: false)

SCRIPT OPTIONS:
    -ScriptHelp          Show this help message
    -ScriptVersion       Show script version
    -ScriptClean         Clean cache directory
    -ScriptUpdate        Force update to latest version
    -Verbose             Enable verbose output

EXAMPLES:
    # Analyze dependencies
    .\jarinker.ps1 analyze -cp libs/ build/classes/java/main
    
    # Shrink JARs
    .\jarinker.ps1 shrink -cp libs/ -o shrunk-libs/ build/classes/java/main
    
    # Use specific version
    `$env:JARINKER_VERSION="0.1.0-RC1"; .\jarinker.ps1 analyze -cp libs/ build/
    
    # Verbose mode
    .\jarinker.ps1 -Verbose --help

For jarinker-specific help, run: .\jarinker.ps1 --help
"@
}

# Check if required dependencies are available
function Test-Dependencies {
    Write-Info "Checking dependencies..."
    
    # Check Java
    try {
        $javaVersion = & java -version 2>&1 | Select-String "version" | ForEach-Object { $_.ToString() }
        if (-not $javaVersion) {
            throw "Java not found"
        }
        
        # Extract version number
        $versionMatch = $javaVersion | Select-String '"(\d+)\..*"' -AllMatches
        if ($versionMatch.Matches.Count -eq 0) {
            # Try newer version format (Java 9+)
            $versionMatch = $javaVersion | Select-String '"(\d+).*"' -AllMatches
        }
        
        if ($versionMatch.Matches.Count -gt 0) {
            $majorVersion = [int]$versionMatch.Matches[0].Groups[1].Value
            if ($majorVersion -lt $Script:REQUIRED_JAVA_VERSION) {
                Write-Error "Java $Script:REQUIRED_JAVA_VERSION or later is required. Found Java $majorVersion."
                Write-Error "Please upgrade your Java installation."
                exit 1
            }
            Write-Success "Java $majorVersion found"
        } else {
            Write-Warn "Could not determine Java version, proceeding anyway"
        }
    }
    catch {
        Write-Error "Java not found. Please install Java $Script:REQUIRED_JAVA_VERSION or later."
        Write-Error "Visit: https://adoptium.net/ to download OpenJDK"
        exit 1
    }
    
    Write-Success "All dependencies satisfied"
}

# Get the latest version from GitHub API
function Get-LatestVersion {
    Write-Info "Fetching latest version from GitHub..."
    
    $apiUrl = "https://api.github.com/repos/$Script:GITHUB_REPO/releases/latest"
    
    try {
        $response = Invoke-RestMethod -Uri $apiUrl -ErrorAction Stop
        $version = $response.tag_name
        
        if (-not $version) {
            throw "No version found in response"
        }
        
        return $version
    }
    catch {
        Write-Error "Failed to fetch latest version from GitHub API"
        Write-Error "Please check your internet connection or try specifying a version with JARINKER_VERSION"
        exit 1
    }
}

# Get download URL for a specific version
function Get-DownloadUrl {
    param([string]$Version)
    
    Write-Info "Getting download URL for version $Version..."
    
    $apiUrl = "https://api.github.com/repos/$Script:GITHUB_REPO/releases/tags/$Version"
    
    try {
        $response = Invoke-RestMethod -Uri $apiUrl -ErrorAction Stop
        $asset = $response.assets | Where-Object { $_.name -like "*jarinker-cli*.zip" } | Select-Object -First 1
        
        if (-not $asset) {
            throw "No suitable download found"
        }
        
        return $asset.browser_download_url
    }
    catch {
        Write-Error "Failed to fetch release information for version $Version"
        Write-Error "Please check if the version exists or try a different version"
        exit 1
    }
}

# Download and extract jarinker
function Install-Jarinker {
    param(
        [string]$Version,
        [string]$DownloadUrl
    )
    
    $cacheDir = Join-Path $Script:JARINKER_CACHE_DIR "versions\$Version"
    $zipFile = Join-Path $cacheDir "jarinker-cli-$Version.zip"
    
    Write-Info "Downloading jarinker $Version..."
    
    # Create cache directory
    if (-not (Test-Path $cacheDir)) {
        New-Item -ItemType Directory -Path $cacheDir -Force | Out-Null
    }
    
    # Download file
    try {
        Invoke-WebRequest -Uri $DownloadUrl -OutFile $zipFile -ErrorAction Stop
    }
    catch {
        Write-Error "Download failed: $_"
        exit 1
    }
    
    if (-not (Test-Path $zipFile)) {
        Write-Error "Download failed: $zipFile not found"
        exit 1
    }
    
    Write-Info "Extracting jarinker..."
    
    # Extract to cache directory
    try {
        Expand-Archive -Path $zipFile -DestinationPath $cacheDir -Force -ErrorAction Stop
    }
    catch {
        Write-Error "Failed to extract $zipFile : $_"
        Remove-Item $zipFile -Force -ErrorAction SilentlyContinue
        exit 1
    }
    
    # Find the jarinker executable
    $jarinkerBin = Get-ChildItem -Path $cacheDir -Recurse -Name "jarinker.bat" | Select-Object -First 1
    
    if (-not $jarinkerBin) {
        Write-Error "jarinker.bat executable not found in extracted files"
        exit 1
    }
    
    $fullPath = Join-Path $cacheDir $jarinkerBin
    Write-Success "jarinker $Version downloaded and extracted"
    return $fullPath
}

# Get or download jarinker
function Get-Jarinker {
    param([string]$Version)
    
    $cacheDir = Join-Path $Script:JARINKER_CACHE_DIR "versions\$Version"
    
    # Look for existing installation
    $jarinkerBin = Get-ChildItem -Path $cacheDir -Recurse -Name "jarinker.bat" -ErrorAction SilentlyContinue | Select-Object -First 1
    
    if ($jarinkerBin) {
        $fullPath = Join-Path $cacheDir $jarinkerBin
        if (Test-Path $fullPath) {
            Write-Success "Using cached jarinker $Version"
            return $fullPath
        }
    }
    
    # Check offline mode
    if ($Script:JARINKER_OFFLINE) {
        Write-Error "jarinker $Version not found in cache and offline mode is enabled"
        Write-Error "Cache directory: $cacheDir"
        exit 1
    }
    
    # Download jarinker
    $downloadUrl = Get-DownloadUrl -Version $Version
    return Install-Jarinker -Version $Version -DownloadUrl $downloadUrl
}

# Execute jarinker with provided arguments
function Invoke-Jarinker {
    param(
        [string]$JarinkerPath,
        [string[]]$Args
    )
    
    Write-Info "Executing: $JarinkerPath $($Args -join ' ')"
    
    # Execute jarinker with all provided arguments
    & $JarinkerPath @Args
    exit $LASTEXITCODE
}

# Clean cache directory
function Clear-Cache {
    if (Test-Path $Script:JARINKER_CACHE_DIR) {
        Write-Info "Cleaning cache directory: $Script:JARINKER_CACHE_DIR"
        Remove-Item $Script:JARINKER_CACHE_DIR -Recurse -Force
        Write-Success "Cache cleaned"
    } else {
        Write-Info "Cache directory does not exist: $Script:JARINKER_CACHE_DIR"
    }
}

# Main function
function Main {
    # Handle script-specific options
    if ($ScriptHelp) {
        Show-Help
        return
    }
    
    if ($ScriptVersion) {
        Write-Host "jarinker remote execution script v1.0.0"
        return
    }
    
    if ($ScriptClean) {
        Clear-Cache
        return
    }
    
    if ($ScriptUpdate) {
        $Script:JARINKER_VERSION = "latest"
        # Force re-download by removing cache
        if ($Script:JARINKER_VERSION -eq "latest") {
            $latestVersion = Get-LatestVersion
            $versionCacheDir = Join-Path $Script:JARINKER_CACHE_DIR "versions\$latestVersion"
            if (Test-Path $versionCacheDir) {
                Remove-Item $versionCacheDir -Recurse -Force
            }
        }
    }
    
    # Check dependencies
    Test-Dependencies
    
    # Determine version to use
    $version = $Script:JARINKER_VERSION
    if ($version -eq "latest") {
        $version = Get-LatestVersion
        Write-Info "Latest version: $version"
    }
    
    # Ensure jarinker is available
    $jarinkerBin = Get-Jarinker -Version $version
    
    # Execute jarinker
    Invoke-Jarinker -JarinkerPath $jarinkerBin -Args $Arguments
}

# Run main function
Main
