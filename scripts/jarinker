#!/bin/bash

# Jarinker Remote Execution Script
# This script automatically downloads and executes jarinker without requiring manual installation
# Usage: curl -sSL https://raw.githubusercontent.com/DanielLiu1123/jarinker/main/scripts/jarinker | bash -s -- [jarinker-args]

set -euo pipefail

# Global configuration
readonly SCRIPT_NAME="jarinker"
readonly GITHUB_REPO="DanielLiu1123/jarinker"
readonly REQUIRED_JAVA_VERSION=17
readonly DEFAULT_CACHE_DIR="$HOME/.jarinker"

# User configurable variables
JARINKER_CACHE_DIR="${JARINKER_CACHE_DIR:-$DEFAULT_CACHE_DIR}"
JARINKER_VERSION="${JARINKER_VERSION:-latest}"
JARINKER_OFFLINE="${JARINKER_OFFLINE:-false}"
JARINKER_VERBOSE="${JARINKER_VERBOSE:-false}"

# Colors for output
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly NC='\033[0m' # No Color

# Logging functions
log_info() {
    if [[ "$JARINKER_VERBOSE" == "true" ]]; then
        echo -e "${BLUE}[INFO]${NC} $1" >&2
    fi
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1" >&2
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

log_success() {
    if [[ "$JARINKER_VERBOSE" == "true" ]]; then
        echo -e "${GREEN}[SUCCESS]${NC} $1" >&2
    fi
}

# Show help information
show_help() {
    cat << 'EOF'
Jarinker Remote Execution Script

USAGE:
    # Direct execution (recommended)
    curl -sSL https://raw.githubusercontent.com/DanielLiu1123/jarinker/main/scripts/jarinker | bash -s -- [JARINKER_ARGS]
    
    # Download and use
    curl -sSL https://raw.githubusercontent.com/DanielLiu1123/jarinker/main/scripts/jarinker -o jarinker && chmod +x jarinker
    ./jarinker [JARINKER_ARGS]

ENVIRONMENT VARIABLES:
    JARINKER_VERSION     Specify jarinker version (default: latest)
    JARINKER_CACHE_DIR   Cache directory (default: ~/.jarinker)
    JARINKER_OFFLINE     Use offline mode, only use cached versions (default: false)
    JARINKER_VERBOSE     Enable verbose output (default: false)

SCRIPT OPTIONS:
    --script-help        Show this help message
    --script-version     Show script version
    --script-clean       Clean cache directory
    --script-update      Force update to latest version

EXAMPLES:
    # Analyze dependencies
    ./jarinker analyze -cp libs/ build/classes/java/main
    
    # Shrink JARs
    ./jarinker shrink -cp libs/ -o shrunk-libs/ build/classes/java/main
    
    # Use specific version
    JARINKER_VERSION=0.1.0-RC1 ./jarinker analyze -cp libs/ build/
    
    # Verbose mode
    JARINKER_VERBOSE=true ./jarinker --help

For jarinker-specific help, run: ./jarinker --help
EOF
}

# Check if required dependencies are available
check_dependencies() {
    log_info "Checking dependencies..."
    
    # Check Java
    if ! command -v java &> /dev/null; then
        log_error "Java not found. Please install Java $REQUIRED_JAVA_VERSION or later."
        log_error "Visit: https://adoptium.net/ to download OpenJDK"
        exit 1
    fi
    
    # Check Java version
    local java_version
    java_version=$(java -version 2>&1 | head -n1 | cut -d'"' -f2 | cut -d'.' -f1)
    if [[ "$java_version" -lt "$REQUIRED_JAVA_VERSION" ]]; then
        log_error "Java $REQUIRED_JAVA_VERSION or later is required. Found Java $java_version."
        log_error "Please upgrade your Java installation."
        exit 1
    fi
    
    log_success "Java $java_version found"
    
    # Check required tools
    local missing_tools=()
    for tool in curl unzip; do
        if ! command -v "$tool" &> /dev/null; then
            missing_tools+=("$tool")
        fi
    done
    
    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        log_error "Missing required tools: ${missing_tools[*]}"
        log_error "Please install the missing tools and try again."
        exit 1
    fi
    
    log_success "All dependencies satisfied"
}

# Get the latest version from GitHub API
get_latest_version() {
    log_info "Fetching latest version from GitHub..."
    
    local api_url="https://api.github.com/repos/$GITHUB_REPO/releases/latest"
    local response
    
    if ! response=$(curl -s --fail "$api_url" 2>/dev/null); then
        log_error "Failed to fetch latest version from GitHub API"
        log_error "Please check your internet connection or try specifying a version with JARINKER_VERSION"
        exit 1
    fi
    
    local version
    version=$(echo "$response" | grep '"tag_name":' | sed -E 's/.*"([^"]+)".*/\1/' | head -n1)
    
    if [[ -z "$version" ]]; then
        log_error "Failed to parse version from GitHub API response"
        exit 1
    fi
    
    echo "$version"
}

# Get download URL for a specific version
get_download_url() {
    local version="$1"
    log_info "Getting download URL for version $version..."
    
    local api_url="https://api.github.com/repos/$GITHUB_REPO/releases/tags/$version"
    local response
    
    if ! response=$(curl -s --fail "$api_url" 2>/dev/null); then
        log_error "Failed to fetch release information for version $version"
        exit 1
    fi
    
    local download_url
    download_url=$(echo "$response" | grep '"browser_download_url":.*jarinker-cli.*\.zip"' | sed -E 's/.*"([^"]+)".*/\1/' | head -n1)
    
    if [[ -z "$download_url" ]]; then
        log_error "No suitable download found for version $version"
        log_error "Please check if the version exists or try a different version"
        exit 1
    fi
    
    echo "$download_url"
}

# Download and extract jarinker
download_jarinker() {
    local version="$1"
    local download_url="$2"
    local cache_dir="$JARINKER_CACHE_DIR/versions/$version"
    local zip_file="$cache_dir/jarinker-cli-$version.zip"
    
    log_info "Downloading jarinker $version..."
    
    # Create cache directory
    mkdir -p "$cache_dir"
    
    # Download with progress bar if possible
    if curl --help 2>/dev/null | grep -q '\--progress-bar'; then
        curl --progress-bar -L "$download_url" -o "$zip_file"
    else
        curl -L "$download_url" -o "$zip_file"
    fi
    
    if [[ ! -f "$zip_file" ]]; then
        log_error "Download failed: $zip_file not found"
        exit 1
    fi
    
    log_info "Extracting jarinker..."
    
    # Extract to cache directory
    if ! unzip -q "$zip_file" -d "$cache_dir"; then
        log_error "Failed to extract $zip_file"
        rm -f "$zip_file"
        exit 1
    fi
    
    # Find the jarinker executable
    local jarinker_bin
    jarinker_bin=$(find "$cache_dir" -name "jarinker" -type f -executable | head -n1)
    
    if [[ -z "$jarinker_bin" ]]; then
        log_error "jarinker executable not found in extracted files"
        exit 1
    fi
    
    # Make sure it's executable
    chmod +x "$jarinker_bin"
    
    log_success "jarinker $version downloaded and extracted"
    echo "$jarinker_bin"
}

# Get or download jarinker
ensure_jarinker_available() {
    local version="$1"
    local cache_dir="$JARINKER_CACHE_DIR/versions/$version"
    
    # Look for existing installation
    local jarinker_bin
    jarinker_bin=$(find "$cache_dir" -name "jarinker" -type f -executable 2>/dev/null | head -n1)
    
    if [[ -n "$jarinker_bin" && -x "$jarinker_bin" ]]; then
        log_success "Using cached jarinker $version"
        echo "$jarinker_bin"
        return
    fi
    
    # Check offline mode
    if [[ "$JARINKER_OFFLINE" == "true" ]]; then
        log_error "jarinker $version not found in cache and offline mode is enabled"
        log_error "Cache directory: $cache_dir"
        exit 1
    fi
    
    # Download jarinker
    local download_url
    download_url=$(get_download_url "$version")
    download_jarinker "$version" "$download_url"
}

# Execute jarinker with provided arguments
execute_jarinker() {
    local jarinker_bin="$1"
    shift
    
    log_info "Executing: $jarinker_bin $*"
    
    # Execute jarinker with all provided arguments
    exec "$jarinker_bin" "$@"
}

# Clean cache directory
clean_cache() {
    if [[ -d "$JARINKER_CACHE_DIR" ]]; then
        log_info "Cleaning cache directory: $JARINKER_CACHE_DIR"
        rm -rf "$JARINKER_CACHE_DIR"
        log_success "Cache cleaned"
    else
        log_info "Cache directory does not exist: $JARINKER_CACHE_DIR"
    fi
}

# Parse script-specific arguments
parse_script_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --script-help)
                show_help
                exit 0
                ;;
            --script-version)
                echo "jarinker remote execution script v1.0.0"
                exit 0
                ;;
            --script-clean)
                clean_cache
                exit 0
                ;;
            --script-update)
                JARINKER_VERSION="latest"
                # Force re-download by removing cache
                if [[ "$JARINKER_VERSION" == "latest" ]]; then
                    # Only try to get latest version if not in offline mode
                    if [[ "$JARINKER_OFFLINE" != "true" ]]; then
                        local latest_version
                        latest_version=$(get_latest_version)
                        rm -rf "$JARINKER_CACHE_DIR/versions/$latest_version"
                    fi
                fi
                shift
                ;;
            *)
                # Not a script argument, pass through to jarinker
                break
                ;;
        esac
        shift
    done

    # Return remaining arguments
    echo "$@"
}

# Main function
main() {
    # Handle script-only arguments that don't need jarinker
    case "${1:-}" in
        --script-help)
            show_help
            exit 0
            ;;
        --script-version)
            echo "jarinker remote execution script v1.0.0"
            exit 0
            ;;
        --script-clean)
            clean_cache
            exit 0
            ;;
    esac

    # Parse script-specific arguments first
    local remaining_args
    remaining_args=$(parse_script_args "$@")

    # Check dependencies
    check_dependencies

    # Determine version to use
    local version="$JARINKER_VERSION"
    if [[ "$version" == "latest" ]]; then
        version=$(get_latest_version)
        log_info "Latest version: $version"
    fi

    # Ensure jarinker is available
    local jarinker_bin
    jarinker_bin=$(ensure_jarinker_available "$version")

    # Execute jarinker
    execute_jarinker "$jarinker_bin" $remaining_args
}

# Run main function if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
