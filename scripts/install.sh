#!/bin/bash

# Jarinker Installation Script
# This script installs the jarinker remote execution script to your system
# Usage: curl -sSL https://raw.githubusercontent.com/DanielLiu1123/jarinker/main/scripts/install.sh | bash

set -euo pipefail

# Configuration
readonly SCRIPT_NAME="jarinker"
readonly GITHUB_REPO="DanielLiu1123/jarinker"
readonly SCRIPT_URL="https://raw.githubusercontent.com/DanielLiu1123/jarinker/main/scripts/jarinker"

# Colors for output
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly NC='\033[0m' # No Color

# Installation directory
INSTALL_DIR="$HOME/.local/bin"

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

# Show help information
show_help() {
    cat << 'EOF'
Jarinker Installation Script

This script downloads and installs the jarinker remote execution script to your system.

USAGE:
    curl -sSL https://raw.githubusercontent.com/DanielLiu1123/jarinker/main/scripts/install.sh | bash

OPTIONS:
    --help                  Show this help message
    --uninstall            Uninstall jarinker

EXAMPLES:
    # Install jarinker
    curl -sSL https://raw.githubusercontent.com/DanielLiu1123/jarinker/main/scripts/install.sh | bash

After installation, you can use jarinker directly:
    jarinker analyze -cp libs/ build/classes/java/main
    jarinker shrink -cp libs/ -o shrunk-libs/ build/classes/java/main
EOF
}

# Check if required tools are available
check_dependencies() {
    log_info "Checking dependencies..."
    
    local missing_tools=()
    for tool in curl chmod; do
        if ! command -v "$tool" &> /dev/null; then
            missing_tools+=("$tool")
        fi
    done
    
    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        log_error "Missing required tools: ${missing_tools[*]}"
        log_error "Please install the missing tools and try again."
        exit 1
    fi
    
    log_success "All dependencies satisfied"
}

# Create installation directory if it doesn't exist
create_install_dir() {
    if [[ ! -d "$INSTALL_DIR" ]]; then
        log_info "Creating installation directory: $INSTALL_DIR"
        if ! mkdir -p "$INSTALL_DIR"; then
            log_error "Failed to create installation directory: $INSTALL_DIR"
            log_error "Please check permissions or specify a different directory with --install-dir"
            exit 1
        fi
    fi
}

# Download and install jarinker script
install_jarinker() {
    local script_path="$INSTALL_DIR/$SCRIPT_NAME"
    
    log_info "Downloading jarinker script from: $SCRIPT_URL"
    
    # Download the script
    if ! curl -sSL "$SCRIPT_URL" -o "$script_path"; then
        log_error "Failed to download jarinker script"
        log_error "Please check your internet connection and try again"
        exit 1
    fi
    
    # Make it executable
    if ! chmod +x "$script_path"; then
        log_error "Failed to make script executable: $script_path"
        exit 1
    fi
    
    log_success "jarinker installed to: $script_path"
}

# Check if installation directory is in PATH
check_path() {
    if [[ ":$PATH:" != *":$INSTALL_DIR:"* ]]; then
        log_warn "Installation directory is not in your PATH: $INSTALL_DIR"
        log_warn "Add the following line to your shell profile (~/.bashrc, ~/.zshrc, etc.):"
        echo ""
        echo "    export PATH=\"$INSTALL_DIR:\$PATH\""
        echo ""
        log_warn "Or run jarinker with full path: $INSTALL_DIR/$SCRIPT_NAME"
    else
        log_success "Installation directory is in your PATH"
    fi
}

# Uninstall jarinker
uninstall_jarinker() {
    local script_path="$INSTALL_DIR/$SCRIPT_NAME"
    
    if [[ -f "$script_path" ]]; then
        log_info "Removing jarinker script: $script_path"
        if rm "$script_path"; then
            log_success "jarinker uninstalled successfully"
        else
            log_error "Failed to remove jarinker script"
            exit 1
        fi
    else
        log_warn "jarinker script not found: $script_path"
    fi
    
    # Also clean up cache if it exists
    local cache_dir="$HOME/.jarinker"
    if [[ -d "$cache_dir" ]]; then
        log_info "Cleaning up cache directory: $cache_dir"
        if rm -rf "$cache_dir"; then
            log_success "Cache directory cleaned"
        else
            log_warn "Failed to clean cache directory: $cache_dir"
        fi
    fi
}

# Test installation
test_installation() {
    local script_path="$INSTALL_DIR/$SCRIPT_NAME"
    
    log_info "Testing installation..."
    
    if [[ ! -f "$script_path" ]]; then
        log_error "Installation failed: script not found at $script_path"
        exit 1
    fi
    
    if [[ ! -x "$script_path" ]]; then
        log_error "Installation failed: script is not executable"
        exit 1
    fi
    
    # Test if script can show help
    if "$script_path" --script-help >/dev/null 2>&1; then
        log_success "Installation test passed"
    else
        log_warn "Installation test failed, but script exists and is executable"
        log_warn "You may need to install Java 17+ to use jarinker"
    fi
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --help)
                show_help
                exit 0
                ;;
            --uninstall)
                uninstall_jarinker
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                log_error "Use --help for usage information"
                exit 1
                ;;
        esac
        shift
    done
}

# Main installation function
main() {
    echo "Jarinker Installation Script"
    echo "============================"
    echo ""
    
    # Parse arguments
    parse_args "$@"
    
    # Check dependencies
    check_dependencies
    
    # Create installation directory
    create_install_dir
    
    # Install jarinker
    install_jarinker
    
    # Test installation
    test_installation
    
    # Check PATH
    check_path
    
    echo ""
    log_success "Installation completed successfully!"
    echo ""
    echo "You can now use jarinker:"
    echo "  jarinker --help"
    echo "  jarinker analyze -cp libs/ build/classes/java/main"
    echo "  jarinker shrink -cp libs/ -o shrunk-libs/ build/classes/java/main"
    echo ""
    echo "For more information, visit: https://github.com/$GITHUB_REPO"
}

# Run main function if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
