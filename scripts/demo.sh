#!/bin/bash

# Jarinker Remote Execution Demo
# This script demonstrates the various ways to use jarinker remotely

set -euo pipefail

# Colors for output
readonly GREEN='\033[0;32m'
readonly BLUE='\033[0;34m'
readonly YELLOW='\033[1;33m'
readonly NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Jarinker Remote Execution Demo${NC}"
echo "=================================="
echo ""

# Function to run a demo step
demo_step() {
    local title="$1"
    local command="$2"
    
    echo -e "${YELLOW}📋 $title${NC}"
    echo "Command: $command"
    echo ""
    
    # Ask user if they want to run this step
    read -p "Run this step? (y/N): " -n 1 -r
    echo ""
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "Running..."
        eval "$command"
        echo ""
        echo -e "${GREEN}✅ Step completed${NC}"
    else
        echo "⏭️  Skipped"
    fi
    echo ""
    echo "----------------------------------------"
    echo ""
}

# Check if we're in the jarinker project directory
if [[ ! -f "build.gradle" ]] || [[ ! -d "jarinker-cli" ]]; then
    echo "❌ This demo should be run from the jarinker project root directory"
    echo "Please cd to the jarinker project directory and run: ./scripts/demo.sh"
    exit 1
fi

echo "This demo will show you different ways to use jarinker remotely."
echo "Make sure you have Java 17+ installed and network connectivity."
echo ""

# Build the example project first
demo_step "Build Example Project" \
    "./gradlew :examples:quick-start:build --no-daemon"

# Demo 1: Direct execution
demo_step "Demo 1: Direct Execution (One-liner)" \
    "curl -sSL https://raw.githubusercontent.com/DanielLiu1123/jarinker/main/scripts/jarinker | bash -s -- analyze -cp \$(pwd)/examples/quick-start/libs \$(pwd)/examples/quick-start/build/classes/java/main"

# Demo 2: Download and use
demo_step "Demo 2: Download Script and Use" \
    "curl -sSL https://raw.githubusercontent.com/DanielLiu1123/jarinker/main/scripts/jarinker -o jarinker-remote && chmod +x jarinker-remote && ./jarinker-remote analyze -cp \$(pwd)/examples/quick-start/libs \$(pwd)/examples/quick-start/build/classes/java/main"

# Demo 3: Install and use
demo_step "Demo 3: Install Script Globally" \
    "curl -sSL https://raw.githubusercontent.com/DanielLiu1123/jarinker/main/scripts/install.sh | bash && export PATH=\"\$HOME/.local/bin:\$PATH\" && jarinker analyze -cp \$(pwd)/examples/quick-start/libs \$(pwd)/examples/quick-start/build/classes/java/main"

# Demo 4: Shrink JARs
demo_step "Demo 4: Shrink JARs" \
    "./jarinker-remote shrink -cp \$(pwd)/examples/quick-start/libs -o \$(pwd)/examples/quick-start/demo-shrunk-libs \$(pwd)/examples/quick-start/build/classes/java/main"

# Demo 5: Compare sizes
demo_step "Demo 5: Compare Original vs Shrunk JAR Sizes" \
    "echo 'Original JARs:' && du -sh examples/quick-start/libs/guava-*.jar && echo 'Shrunk JARs:' && du -sh examples/quick-start/demo-shrunk-libs/guava-*.jar 2>/dev/null || echo 'No shrunk JARs found (previous step may have been skipped)'"

# Demo 6: Version management
demo_step "Demo 6: Use Specific Version" \
    "JARINKER_VERSION=latest JARINKER_VERBOSE=true ./jarinker-remote --script-version"

# Demo 7: Offline mode
demo_step "Demo 7: Offline Mode (using cached version)" \
    "JARINKER_OFFLINE=true JARINKER_VERBOSE=true ./jarinker-remote --help"

# Demo 8: Cache management
demo_step "Demo 8: View Cache and Clean Up" \
    "echo 'Cache directory contents:' && ls -la ~/.jarinker/ 2>/dev/null || echo 'No cache directory found' && echo '' && echo 'Cleaning cache...' && ./jarinker-remote --script-clean"

# Cleanup
echo -e "${BLUE}🧹 Cleanup${NC}"
echo "Removing demo files..."

# Remove downloaded script
if [[ -f "jarinker-remote" ]]; then
    rm jarinker-remote
    echo "✅ Removed jarinker-remote script"
fi

# Remove demo shrunk libs
if [[ -d "examples/quick-start/demo-shrunk-libs" ]]; then
    rm -rf examples/quick-start/demo-shrunk-libs
    echo "✅ Removed demo shrunk libs"
fi

# Remove demo installation
if [[ -f "$HOME/.local/bin/jarinker" ]]; then
    echo "ℹ️  jarinker is installed in ~/.local/bin/jarinker"
    echo "   Run 'curl -sSL https://raw.githubusercontent.com/DanielLiu1123/jarinker/main/scripts/install.sh | bash -s -- --uninstall' to remove it"
fi

echo ""
echo -e "${GREEN}🎉 Demo completed!${NC}"
echo ""
echo "Summary of what you learned:"
echo "• How to use jarinker with one-line commands"
echo "• How to download and use the remote script"
echo "• How to install jarinker globally"
echo "• How to shrink JARs and compare sizes"
echo "• How to manage versions and use offline mode"
echo "• How to manage the cache"
echo ""
echo "For more information:"
echo "• Project README: https://github.com/DanielLiu1123/jarinker"
echo "• Script documentation: https://github.com/DanielLiu1123/jarinker/blob/main/scripts/README.md"
echo "• Issues and support: https://github.com/DanielLiu1123/jarinker/issues"
echo ""
echo "Happy JAR shrinking! 🎯"
