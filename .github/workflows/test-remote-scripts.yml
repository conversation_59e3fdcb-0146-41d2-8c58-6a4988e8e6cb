name: Test Remote Scripts

on:
  push:
    branches: [ main ]
    paths:
      - 'scripts/**'
      - '.github/workflows/test-remote-scripts.yml'
  pull_request:
    branches: [ main ]
    paths:
      - 'scripts/**'
      - '.github/workflows/test-remote-scripts.yml'

jobs:
  test-bash-script:
    name: Test Bash Script
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, macos-latest]
        java-version: [17, 21]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Set up JDK ${{ matrix.java-version }}
        uses: actions/setup-java@v4
        with:
          java-version: ${{ matrix.java-version }}
          distribution: 'corretto'
      
      - name: Make script executable
        run: chmod +x scripts/jarinker
      
      - name: Test script help
        run: ./scripts/jarinker --script-help
      
      - name: Test script version
        run: ./scripts/jarinker --script-version
      
      - name: Test dependency check
        run: |
          # This should pass since we have Java installed
          JARINKER_VERBOSE=true ./scripts/jarinker --script-help > /dev/null
      
      - name: Test cache clean
        run: ./scripts/jarinker --script-clean
      
      - name: Build test project
        run: |
          ./gradlew :examples:quick-start:build --no-daemon
      
      - name: Test jarinker analyze (with actual download)
        run: |
          JARINKER_VERBOSE=true ./scripts/jarinker analyze \
            -cp $(pwd)/examples/quick-start/libs \
            $(pwd)/examples/quick-start/build/classes/java/main
      
      - name: Test jarinker shrink (using cached version)
        run: |
          JARINKER_VERBOSE=true ./scripts/jarinker shrink \
            -cp $(pwd)/examples/quick-start/libs \
            -o $(pwd)/examples/quick-start/test-shrunk-libs \
            $(pwd)/examples/quick-start/build/classes/java/main
      
      - name: Verify shrunk files exist
        run: |
          ls -la examples/quick-start/test-shrunk-libs/
          # Check that at least one JAR was shrunk
          [ $(ls examples/quick-start/test-shrunk-libs/*.jar | wc -l) -gt 0 ]
      
      - name: Test offline mode
        run: |
          # This should work since we already downloaded jarinker
          JARINKER_OFFLINE=true JARINKER_VERBOSE=true ./scripts/jarinker --help

  test-install-script:
    name: Test Install Script
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, macos-latest]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Set up JDK 17
        uses: actions/setup-java@v4
        with:
          java-version: '17'
          distribution: 'corretto'
      
      - name: Make install script executable
        run: chmod +x scripts/install.sh
      
      - name: Test install script help
        run: ./scripts/install.sh --help
      
      - name: Test installation to default directory
        run: |
          ./scripts/install.sh

      - name: Verify installation
        run: |
          [ -f $HOME/.local/bin/jarinker ]
          [ -x $HOME/.local/bin/jarinker ]

      - name: Test installed jarinker
        run: |
          $HOME/.local/bin/jarinker --script-help

      - name: Test uninstall
        run: |
          ./scripts/install.sh --uninstall
          [ ! -f $HOME/.local/bin/jarinker ]

  test-powershell-script:
    name: Test PowerShell Script
    runs-on: windows-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Set up JDK 17
        uses: actions/setup-java@v4
        with:
          java-version: '17'
          distribution: 'corretto'
      
      - name: Test PowerShell script help
        run: |
          .\scripts\jarinker.ps1 -ScriptHelp
        shell: powershell
      
      - name: Test PowerShell script version
        run: |
          .\scripts\jarinker.ps1 -ScriptVersion
        shell: powershell
      
      - name: Test dependency check
        run: |
          $env:JARINKER_VERBOSE="true"
          .\scripts\jarinker.ps1 -ScriptHelp | Out-Null
        shell: powershell
      
      - name: Test cache clean
        run: |
          .\scripts\jarinker.ps1 -ScriptClean
        shell: powershell
      
      - name: Build test project
        run: |
          .\gradlew.bat :examples:quick-start:build --no-daemon
        shell: cmd
      
      - name: Test jarinker analyze
        run: |
          $env:JARINKER_VERBOSE="true"
          .\scripts\jarinker.ps1 analyze -cp "$(Get-Location)\examples\quick-start\libs" "$(Get-Location)\examples\quick-start\build\classes\java\main"
        shell: powershell
      
      - name: Test jarinker shrink
        run: |
          $env:JARINKER_VERBOSE="true"
          .\scripts\jarinker.ps1 shrink -cp "$(Get-Location)\examples\quick-start\libs" -o "$(Get-Location)\examples\quick-start\test-shrunk-libs" "$(Get-Location)\examples\quick-start\build\classes\java\main"
        shell: powershell
      
      - name: Verify shrunk files exist
        run: |
          Get-ChildItem examples\quick-start\test-shrunk-libs\*.jar
          if ((Get-ChildItem examples\quick-start\test-shrunk-libs\*.jar).Count -eq 0) { exit 1 }
        shell: powershell

  test-error-conditions:
    name: Test Error Conditions
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Set up JDK 17
        uses: actions/setup-java@v4
        with:
          java-version: '17'
          distribution: 'corretto'
      
      - name: Make script executable
        run: chmod +x scripts/jarinker
      
      - name: Test offline mode without cache (should fail)
        run: |
          # Clean cache first
          ./scripts/jarinker --script-clean
          
          # This should fail because no cache exists and we're in offline mode
          if JARINKER_OFFLINE=true ./scripts/jarinker --help 2>/dev/null; then
            echo "ERROR: Offline mode should have failed without cache"
            exit 1
          else
            echo "SUCCESS: Offline mode correctly failed without cache"
          fi
      
      - name: Test invalid version (should fail gracefully)
        run: |
          # This should fail gracefully
          if JARINKER_VERSION=invalid-version-12345 ./scripts/jarinker --help 2>/dev/null; then
            echo "ERROR: Invalid version should have failed"
            exit 1
          else
            echo "SUCCESS: Invalid version correctly failed"
          fi

  test-integration:
    name: Integration Test
    runs-on: ubuntu-latest
    needs: [test-bash-script, test-install-script]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Set up JDK 17
        uses: actions/setup-java@v4
        with:
          java-version: '17'
          distribution: 'corretto'
      
      - name: Build project
        run: ./gradlew build --no-daemon
      
      - name: Test end-to-end workflow
        run: |
          # Install jarinker
          curl -sSL https://raw.githubusercontent.com/DanielLiu1123/jarinker/main/scripts/install.sh | bash

          # Add to PATH
          export PATH="$HOME/.local/bin:$PATH"
          
          # Build example project
          ./gradlew :examples:quick-start:build --no-daemon
          
          # Use installed jarinker
          jarinker analyze -cp $(pwd)/examples/quick-start/libs $(pwd)/examples/quick-start/build/classes/java/main
          
          # Shrink JARs
          jarinker shrink -cp $(pwd)/examples/quick-start/libs -o $(pwd)/examples/quick-start/final-shrunk-libs $(pwd)/examples/quick-start/build/classes/java/main
          
          # Verify results
          ls -la examples/quick-start/final-shrunk-libs/
          
          # Test that shrunk JARs still work
          ./gradlew :examples:quick-start:run --no-daemon
